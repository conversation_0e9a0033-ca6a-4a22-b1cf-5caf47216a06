/* 川麻小助手样式 - 竹子青色主题 */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #2E8B57 0%, #3CB371 30%, #90EE90 70%, #98FB98 100%);
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 20rpx;
  min-height: 100vh;
}

/* 标题区域 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx 0;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #F0FFF0;
  text-shadow: 2rpx 2rpx 4rpx rgba(0,0,0,0.3);
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #E0FFE0;
  display: block;
}

/* 总分显示区域 */
.total-score-section {
  background: rgba(240, 255, 240, 0.95);
  border-radius: 25rpx;
  margin-bottom: 30rpx;
  padding: 40rpx 30rpx;
  box-shadow: 0 10rpx 25rpx rgba(0,0,0,0.2);
  border: 3rpx solid #228B22;
  text-align: center;
}

.total-score-container {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 15rpx;
  margin-bottom: 20rpx;
}

.total-score-label {
  font-size: 36rpx;
  font-weight: bold;
  color: #2E8B57;
}

.total-score-value {
  font-size: 80rpx;
  font-weight: bold;
  color: #228B22;
  text-shadow: 2rpx 2rpx 4rpx rgba(0,0,0,0.1);
}

.total-score-value.capped {
  color: #FF6347;
}

.total-score-unit {
  font-size: 32rpx;
  color: #2E8B57;
  font-weight: bold;
}

.cap-indicator-large {
  font-size: 28rpx;
  color: #FF6347;
  background: rgba(255, 99, 71, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 15rpx;
  margin-left: 20rpx;
  border: 2rpx solid #FF6347;
}

.score-breakdown {
  display: flex;
  justify-content: center;
  gap: 30rpx;
  flex-wrap: wrap;
}

.breakdown-item {
  font-size: 24rpx;
  color: #2E8B57;
  background: rgba(46, 139, 87, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  border: 1rpx solid #2E8B57;
}

/* 区块样式 */
.section {
  background: rgba(240, 255, 240, 0.95);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  width: 80%;
  padding: 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0,0,0,0.15);
  border: 2rpx solid #228B22;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #228B22;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #2E8B57;
}

.reset-btn {
  font-size: 26rpx;
  color: #228B22;
  padding: 8rpx 16rpx;
  border: 1rpx solid #228B22;
  border-radius: 10rpx;
  background: rgba(34, 139, 34, 0.1);
}

/* 分数设置 */
.score-settings {
  display: flex;
  gap: 30rpx;
  flex-wrap: wrap;
}

.score-input-group {
  flex: 1;
  min-width: 200rpx;
  display: flex;
  align-items: center;
  gap: 10rpx;
  background: #FFF;
  border: 2rpx solid #228B22;
  border-radius: 15rpx;
  padding: 20rpx;
}

.input-label {
  font-size: 28rpx;
  font-weight: bold;
  color: #2E8B57;
  min-width: 60rpx;
}

.score-input {
  flex: 1;
  font-size: 28rpx;
  color: #2E8B57;
  text-align: center;
  background: transparent;
}

.input-unit {
  font-size: 24rpx;
  color: #3CB371;
}

/* 胡牌类型网格 */
.hu-types-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15rpx;
}

/* 根数类型网格 */
.root-types-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.hu-type-item {
  background: #FFF;
  border: 2rpx solid #228B22;
  border-radius: 15rpx;
  padding: 20rpx 10rpx;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}

.hu-type-item.selected {
  background: #228B22;
  transform: scale(0.95);
  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}

.hu-type-name {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #2E8B57;
  margin-bottom: 8rpx;
}

.hu-type-item.selected .hu-type-name {
  color: white;
}

.hu-type-score {
  display: block;
  font-size: 22rpx;
  color: #3CB371;
}

.hu-type-item.selected .hu-type-score {
  color: #F0FFF0;
}

/* 加分项 */
.bonus-container {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}

.bonus-item {
  flex: 1;
  min-width: 200rpx;
  background: #FFF;
  border: 2rpx solid #228B22;
  border-radius: 15rpx;
  padding: 25rpx 15rpx;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}

.bonus-item.selected {
  background: #228B22;
  transform: scale(0.95);
}

.bonus-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #2E8B57;
  margin-bottom: 8rpx;
}

.bonus-item.selected .bonus-name {
  color: white;
}

.bonus-score {
  display: block;
  font-size: 24rpx;
  color: #3CB371;
}

.bonus-item.selected .bonus-score {
  color: #F0FFF0;
}

/* 加分项分组 */
.bonus-group {
  margin-bottom: 25rpx;
}

.bonus-group:last-child {
  margin-bottom: 0;
}

.bonus-group-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #2E8B57;
  margin-bottom: 15rpx;
  padding-bottom: 8rpx;
  border-bottom: 1rpx solid rgba(34, 139, 34, 0.3);
}

/* 计数器样式 */
.counter-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.counter-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #FFF;
  border: 2rpx solid #228B22;
  border-radius: 15rpx;
  padding: 20rpx;
}

.counter-label {
  font-size: 26rpx;
  font-weight: bold;
  color: #2E8B57;
  flex: 1;
}

.counter-controls {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.counter-btn {
  width: 60rpx;
  height: 60rpx;
  background: #228B22;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.counter-btn:active {
  transform: scale(0.9);
  background: #32CD32;
}

.counter-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #2E8B57;
  min-width: 40rpx;
  text-align: center;
}

.counter-score {
  font-size: 24rpx;
  color: #3CB371;
  min-width: 80rpx;
  text-align: right;
}

/* 操作按钮区域 */
.action-section {
  display: flex;
  gap: 20rpx;
  margin-bottom: 30rpx;
}

.calculate-btn, .reset-all-btn {
  flex: 1;
  text-align: center;
  padding: 25rpx;
  border-radius: 15rpx;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
  color: white;
}

.calculate-btn {
  background: linear-gradient(135deg, #228B22 0%, #32CD32 100%);
  box-shadow: 0 6rpx 15rpx rgba(34, 139, 34, 0.3);
}

.reset-all-btn {
  background: linear-gradient(135deg, #3CB371 0%, #90EE90 100%);
  box-shadow: 0 6rpx 15rpx rgba(60, 179, 113, 0.3);
}

.calculate-btn:active, .reset-all-btn:active {
  transform: scale(0.98);
  box-shadow: 0 3rpx 8rpx rgba(34, 139, 34, 0.4);
}


