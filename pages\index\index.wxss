/* 川麻小助手样式 */
page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: linear-gradient(135deg, #8B4513 0%, #A0522D 50%, #CD853F 100%);
}

.scrollarea {
  flex: 1;
  overflow-y: hidden;
}

.container {
  padding: 20rpx;
  min-height: 100vh;
}

/* 标题区域 */
.header {
  text-align: center;
  margin-bottom: 40rpx;
  padding: 30rpx 0;
}

.title {
  font-size: 48rpx;
  font-weight: bold;
  color: #FFF8DC;
  text-shadow: 2rpx 2rpx 4rpx rgba(0,0,0,0.3);
  display: block;
  margin-bottom: 10rpx;
}

.subtitle {
  font-size: 28rpx;
  color: #F5DEB3;
  display: block;
}

/* 区块样式 */
.section {
  background: rgba(255, 248, 220, 0.95);
  border-radius: 20rpx;
  margin-bottom: 30rpx;
  width: 80%;
  padding: 30rpx;
  box-shadow: 0 8rpx 20rpx rgba(0,0,0,0.15);
  border: 2rpx solid #D2691E;
}

.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 25rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #D2691E;
}

.title-text {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
}

.reset-btn {
  font-size: 26rpx;
  color: #D2691E;
  padding: 8rpx 16rpx;
  border: 1rpx solid #D2691E;
  border-radius: 10rpx;
  background: rgba(210, 105, 30, 0.1);
}

/* 分数设置 */
.score-settings {
  display: flex;
  gap: 30rpx;
  flex-wrap: wrap;
}

.score-input-group {
  flex: 1;
  min-width: 200rpx;
  display: flex;
  align-items: center;
  gap: 10rpx;
  background: #FFF;
  border: 2rpx solid #D2691E;
  border-radius: 15rpx;
  padding: 20rpx;
}

.input-label {
  font-size: 28rpx;
  font-weight: bold;
  color: #8B4513;
  min-width: 60rpx;
}

.score-input {
  flex: 1;
  font-size: 28rpx;
  color: #8B4513;
  text-align: center;
  background: transparent;
}

.input-unit {
  font-size: 24rpx;
  color: #A0522D;
}

/* 胡牌类型网格 */
.hu-types-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 15rpx;
}

/* 根数类型网格 */
.root-types-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 15rpx;
}

.hu-type-item {
  background: #FFF;
  border: 2rpx solid #D2691E;
  border-radius: 15rpx;
  padding: 20rpx 10rpx;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}

.hu-type-item.selected {
  background: #D2691E;
  transform: scale(0.95);
  box-shadow: 0 2rpx 4rpx rgba(0,0,0,0.2);
}

.hu-type-name {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 8rpx;
}

.hu-type-item.selected .hu-type-name {
  color: white;
}

.hu-type-score {
  display: block;
  font-size: 22rpx;
  color: #A0522D;
}

.hu-type-item.selected .hu-type-score {
  color: #FFF8DC;
}

/* 加分项 */
.bonus-container {
  display: flex;
  gap: 15rpx;
  flex-wrap: wrap;
}

.bonus-item {
  flex: 1;
  min-width: 200rpx;
  background: #FFF;
  border: 2rpx solid #D2691E;
  border-radius: 15rpx;
  padding: 25rpx 15rpx;
  text-align: center;
  transition: all 0.3s ease;
  box-shadow: 0 4rpx 8rpx rgba(0,0,0,0.1);
}

.bonus-item.selected {
  background: #D2691E;
  transform: scale(0.95);
}

.bonus-name {
  display: block;
  font-size: 28rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 8rpx;
}

.bonus-item.selected .bonus-name {
  color: white;
}

.bonus-score {
  display: block;
  font-size: 24rpx;
  color: #A0522D;
}

.bonus-item.selected .bonus-score {
  color: #FFF8DC;
}

/* 加分项分组 */
.bonus-group {
  margin-bottom: 25rpx;
}

.bonus-group:last-child {
  margin-bottom: 0;
}

.bonus-group-title {
  display: block;
  font-size: 26rpx;
  font-weight: bold;
  color: #8B4513;
  margin-bottom: 15rpx;
  padding-bottom: 8rpx;
  border-bottom: 1rpx solid rgba(210, 105, 30, 0.3);
}

/* 计数器样式 */
.counter-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.counter-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: #FFF;
  border: 2rpx solid #D2691E;
  border-radius: 15rpx;
  padding: 20rpx;
}

.counter-label {
  font-size: 26rpx;
  font-weight: bold;
  color: #8B4513;
  flex: 1;
}

.counter-controls {
  display: flex;
  align-items: center;
  gap: 15rpx;
}

.counter-btn {
  width: 60rpx;
  height: 60rpx;
  background: #D2691E;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.counter-btn:active {
  transform: scale(0.9);
  background: #B8860B;
}

.counter-value {
  font-size: 32rpx;
  font-weight: bold;
  color: #8B4513;
  min-width: 40rpx;
  text-align: center;
}

.counter-score {
  font-size: 24rpx;
  color: #A0522D;
  min-width: 80rpx;
  text-align: right;
}

/* 计算结果区域 */
.result-section {
  background: rgba(255, 248, 220, 0.98);
  border-radius: 20rpx;
  padding: 30rpx;
  width: 80%;
  box-shadow: 0 8rpx 20rpx rgba(0,0,0,0.2);
  border: 3rpx solid #D2691E;
  margin-bottom: 30rpx;
}

.result-container {
  margin-bottom: 30rpx;
}

.base-score, .bonus-total, .final-score {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  border-bottom: 1rpx solid rgba(210, 105, 30, 0.3);
}

.final-score {
  border-bottom: none;
  padding-top: 20rpx;
  margin-top: 15rpx;
  border-top: 2rpx solid #D2691E;
}

.label {
  font-size: 30rpx;
  color: #8B4513;
  font-weight: bold;
}

.score {
  font-size: 32rpx;
  font-weight: bold;
  color: #D2691E;
}

.final-score .score {
  font-size: 40rpx;
  color: #8B4513;
}

.score.capped {
  color: #FF6B35;
}

.cap-indicator {
  font-size: 24rpx;
  color: #FF6B35;
  background: rgba(255, 107, 53, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 10rpx;
  margin-left: 15rpx;
  border: 1rpx solid #FF6B35;
}

/* 计算按钮 */
.calculate-btn {
  background: linear-gradient(135deg, #D2691E 0%, #CD853F 100%);
  color: white;
  text-align: center;
  padding: 25rpx;
  border-radius: 15rpx;
  font-size: 32rpx;
  font-weight: bold;
  box-shadow: 0 6rpx 15rpx rgba(210, 105, 30, 0.3);
  transition: all 0.3s ease;
}

.calculate-btn:active {
  transform: scale(0.98);
  box-shadow: 0 3rpx 8rpx rgba(210, 105, 30, 0.4);
}
