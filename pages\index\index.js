// 川麻小助手 - 算分工具
Page({
    data: {
        // 封顶分数
        capScore: 50,

        // 胡牌类型数据
        huTypes: [
            { id: 'pinghu', name: '平胡', score: 1, selected: false },
            { id: 'duizihu', name: '对子胡', score: 2, selected: false },
            { id: 'qingyise', name: '清一色', score: 4, selected: false },
            { id: 'qidui', name: '七对', score: 4, selected: false },
            { id: 'longqidui', name: '龙七对', score: 8, selected: false },
            { id: 'gangshanghua', name: '杠上花', score: 2, selected: false },
            { id: 'gangshangpao', name: '杠上炮', score: 2, selected: false },
            { id: 'jingougou', name: '金钩钩', score: 4, selected: false },
            { id: 'haidi', name: '海底', score: 2, selected: false },
            { id: 'tianhu', name: '天胡', score: 16, selected: false },
            { id: 'dihu', name: '地胡', score: 16, selected: false },
            { id: 'yigen', name: '1根', score: 2, selected: false },
            { id: 'ergen', name: '2根', score: 4, selected: false },
            { id: 'sangen', name: '3根', score: 8, selected: false },
            { id: 'sigen', name: '4根', score: 16, selected: false }
        ],

        // 加分项数据
        bonuses: [
            { id: 'zimo', name: '自摸', score: 1, selected: false },
            { id: 'gangpai', name: '杠牌', score: 2, selected: false },
            { id: 'bagang', name: '巴杠', score: 1, selected: false }
        ],

        // 计算结果
        baseScore: 0, // 基础分（胡牌类型相乘）
        bonusTotal: 0, // 加分项总和
        finalScore: 0, // 最终得分
        isCapped: false // 是否封顶
    },

    onLoad() {
        this.calculateScore();
    },

    // 封顶分数改变
    onCapScoreChange(e) {
        this.setData({
            capScore: e.detail.value
        });
        this.calculateScore();
    },

    // 切换胡牌类型选择
    toggleHuType(e) {
        const id = e.currentTarget.dataset.id;
        const huTypes = this.data.huTypes.map(item => {
            if (item.id === id) {
                return {...item, selected: !item.selected };
            }
            return item;
        });

        this.setData({ huTypes });
        this.calculateScore();
    },

    // 切换加分项选择
    toggleBonus(e) {
        const id = e.currentTarget.dataset.id;
        const bonuses = this.data.bonuses.map(item => {
            if (item.id === id) {
                return {...item, selected: !item.selected };
            }
            return item;
        });

        this.setData({ bonuses });
        this.calculateScore();
    },

    // 重置胡牌类型
    resetHuTypes() {
        const huTypes = this.data.huTypes.map(item => ({
            ...item,
            selected: false
        }));

        this.setData({ huTypes });
        this.calculateScore();
    },

    // 重置加分项
    resetBonuses() {
        const bonuses = this.data.bonuses.map(item => ({
            ...item,
            selected: false
        }));

        this.setData({ bonuses });
        this.calculateScore();
    },

    // 计算分数
    calculateScore() {
        const { huTypes, bonuses, capScore } = this.data;

        // 计算基础分（选中的胡牌类型分数相乘）
        const selectedHuTypes = huTypes.filter(item => item.selected);
        let baseScore = 0;

        if (selectedHuTypes.length > 0) {
            baseScore = selectedHuTypes.reduce((total, item) => total * item.score, 1);
        }

        // 计算加分项总和
        const bonusTotal = bonuses
            .filter(item => item.selected)
            .reduce((total, item) => total + item.score, 0);

        // 计算最终得分
        let finalScore = baseScore + bonusTotal;
        let isCapped = false;

        // 检查是否超过封顶分数（只有基础分受封顶限制）
        if (baseScore > capScore) {
            finalScore = capScore + bonusTotal;
            isCapped = true;
        }

        this.setData({
            baseScore,
            bonusTotal,
            finalScore,
            isCapped
        });
    }
})