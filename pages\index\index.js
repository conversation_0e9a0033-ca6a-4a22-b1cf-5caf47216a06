// 川麻小助手 - 算分工具
Page({
    data: {
        // 分数设置
        baseUnit: 1, // 底分
        capScore: 16, // 封顶分数

        // 基础胡牌类型数据
        basicHuTypes: [
            { id: 'pinghu', name: '平胡', multiplier: 1, selected: false },
            { id: 'duizihu', name: '对子胡', multiplier: 2, selected: false },
            { id: 'qingyise', name: '清一色', multiplier: 4, selected: false },
            { id: 'qidui', name: '七对', multiplier: 4, selected: false },
            { id: 'longqidui', name: '龙七对', multiplier: 8, selected: false },
            { id: 'gangshanghua', name: '杠上花', multiplier: 2, selected: false },
            { id: 'gangshang<PERSON>o', name: '杠上炮', multiplier: 2, selected: false },
            { id: 'jingougou', name: '金钩钩', multiplier: 4, selected: false },
            { id: 'haidi', name: '海底', multiplier: 2, selected: false },
            { id: 'tianhu', name: '天胡', multiplier: 16, selected: false },
            { id: 'dihu', name: '地胡', multiplier: 16, selected: false }
        ],

        // 根数类型数据
        rootTypes: [
            { id: 'yigen', name: '1根', multiplier: 2, selected: false },
            { id: 'ergen', name: '2根', multiplier: 4, selected: false },
            { id: 'sangen', name: '3根', multiplier: 8, selected: false },
            { id: 'sigen', name: '4根', multiplier: 16, selected: false }
        ],

        // 自摸选项
        zimoAddBase: false, // 自摸加底
        zimoAddFan: false, // 自摸加番

        // 杠牌计数
        gangCount: 0, // 杠牌次数
        bagangCount: 0, // 巴杠次数

        // 计算结果
        baseScore: 0, // 基础分（胡牌类型相乘后乘以底分）
        bonusTotal: 0, // 加分项总和
        finalScore: 0, // 最终得分
        isCapped: false // 是否封顶
    },

    onLoad() {
        this.calculateScore();
    },

    // 底分改变
    onBaseUnitChange(e) {
        const value = parseInt(e.detail.value) || 1;
        this.setData({
            baseUnit: Math.max(1, value)
        });
        this.calculateScore();
    },

    // 封顶分数改变
    onCapScoreChange(e) {
        const value = parseInt(e.detail.value) || 16;
        this.setData({
            capScore: Math.max(1, value)
        });
        this.calculateScore();
    },

    // 切换基础胡牌类型选择
    toggleHuType(e) {
        const id = e.currentTarget.dataset.id;
        const basicHuTypes = this.data.basicHuTypes.map(item => {
            if (item.id === id) {
                return {...item, selected: !item.selected };
            }
            return item;
        });

        this.setData({ basicHuTypes });
        this.calculateScore();
    },

    // 切换根数类型选择
    toggleRootType(e) {
        const id = e.currentTarget.dataset.id;
        const rootTypes = this.data.rootTypes.map(item => {
            if (item.id === id) {
                return {...item, selected: !item.selected };
            }
            return item;
        });

        this.setData({ rootTypes });
        this.calculateScore();
    },

    // 切换自摸加底
    toggleZimoAddBase() {
        this.setData({
            zimoAddBase: !this.data.zimoAddBase
        });
        this.calculateScore();
    },

    // 切换自摸加番
    toggleZimoAddFan() {
        this.setData({
            zimoAddFan: !this.data.zimoAddFan
        });
        this.calculateScore();
    },

    // 改变杠牌次数
    changeGangCount(e) {
        const { type, action } = e.currentTarget.dataset;
        const currentCount = type === 'gang' ? this.data.gangCount : this.data.bagangCount;
        let newCount = currentCount;

        if (action === 'plus' && currentCount < 4) {
            newCount = currentCount + 1;
        } else if (action === 'minus' && currentCount > 0) {
            newCount = currentCount - 1;
        }

        if (type === 'gang') {
            this.setData({ gangCount: newCount });
        } else {
            this.setData({ bagangCount: newCount });
        }
        this.calculateScore();
    },

    // 重置胡牌类型
    resetHuTypes() {
        const basicHuTypes = this.data.basicHuTypes.map(item => ({
            ...item,
            selected: false
        }));

        this.setData({ basicHuTypes });
        this.calculateScore();
    },

    // 重置根数类型
    resetRootTypes() {
        const rootTypes = this.data.rootTypes.map(item => ({
            ...item,
            selected: false
        }));

        this.setData({ rootTypes });
        this.calculateScore();
    },

    // 重置加分项
    resetBonuses() {
        this.setData({
            zimoAddBase: false,
            zimoAddFan: false,
            gangCount: 0,
            bagangCount: 0
        });
        this.calculateScore();
    },

    // 计算分数
    calculateScore() {
        const {
            basicHuTypes,
            rootTypes,
            baseUnit,
            capScore,
            zimoAddBase,
            zimoAddFan,
            gangCount,
            bagangCount
        } = this.data;

        // 计算基础分（选中的胡牌类型倍数相乘，再乘以底分）
        const selectedBasicTypes = basicHuTypes.filter(item => item.selected);
        const selectedRootTypes = rootTypes.filter(item => item.selected);

        let multiplier = 0;

        // 如果有选择胡牌类型，计算倍数
        if (selectedBasicTypes.length > 0 || selectedRootTypes.length > 0) {
            // 基础胡牌类型倍数相乘
            let basicMultiplier = selectedBasicTypes.length > 0 ?
                selectedBasicTypes.reduce((total, item) => total * item.multiplier, 1) :
                1;

            // 根数类型倍数相乘
            let rootMultiplier = selectedRootTypes.length > 0 ?
                selectedRootTypes.reduce((total, item) => total * item.multiplier, 1) :
                1;

            // 如果两种类型都有选择，则相乘；否则取有选择的那种
            if (selectedBasicTypes.length > 0 && selectedRootTypes.length > 0) {
                multiplier = basicMultiplier * rootMultiplier;
            } else if (selectedBasicTypes.length > 0) {
                multiplier = basicMultiplier;
            } else {
                multiplier = rootMultiplier;
            }
        }

        // 基础分 = 倍数 × 底分
        let baseScore = multiplier * baseUnit;

        // 处理自摸加番（在封顶前处理）
        if (zimoAddFan && baseScore > 0) {
            baseScore = baseScore * 2;
        }

        // 检查基础分是否超过封顶
        let isCapped = false;
        if (baseScore > capScore) {
            baseScore = capScore;
            isCapped = true;
        }

        // 计算加分项（不受封顶影响）
        let bonusTotal = 0;

        // 自摸加底
        if (zimoAddBase) {
            bonusTotal += baseUnit;
        }

        // 杠牌加分
        bonusTotal += gangCount * 2;

        // 巴杠加分
        bonusTotal += bagangCount * 1;

        // 最终得分
        const finalScore = baseScore + bonusTotal;

        this.setData({
            baseScore,
            bonusTotal,
            finalScore,
            isCapped
        });
    }
})