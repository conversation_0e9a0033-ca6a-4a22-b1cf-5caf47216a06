<!--川麻小助手主页面-->
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 标题区域 -->
    <view class="header">
      <text class="title">川麻小助手</text>
      <text class="subtitle">专业算分工具</text>
    </view>

    <!-- 分数设置 -->
    <view class="section">
      <view class="section-title">
        <text class="title-text">分数设置</text>
      </view>
      <view class="score-settings">
        <view class="score-input-group">
          <text class="input-label">底分：</text>
          <input class="score-input"
                 type="number"
                 value="{{baseUnit}}"
                 bindinput="onBaseUnitChange"
                 placeholder="1"/>
          <text class="input-unit">分</text>
        </view>
        <view class="score-input-group">
          <text class="input-label">封顶：</text>
          <input class="score-input"
                 type="number"
                 value="{{capScore}}"
                 bindinput="onCapScoreChange"
                 placeholder="16"/>
          <text class="input-unit">分</text>
        </view>
      </view>
    </view>

    <!-- 胡牌类型选择 -->
    <view class="section">
      <view class="section-title">
        <text class="title-text">胡牌类型</text>
        <text class="reset-btn" bindtap="resetHuTypes">重置</text>
      </view>
      <view class="hu-types-grid">
        <view wx:for="{{basicHuTypes}}"
              wx:key="id"
              class="hu-type-item {{item.selected ? 'selected' : ''}}"
              bindtap="toggleHuType"
              data-id="{{item.id}}">
          <text class="hu-type-name">{{item.name}}</text>
          <text class="hu-type-score">{{item.multiplier}}×底分</text>
        </view>
      </view>
    </view>

    <!-- 根数类型 -->
    <view class="section">
      <view class="section-title">
        <text class="title-text">根数类型</text>
        <text class="reset-btn" bindtap="resetRootTypes">重置</text>
      </view>
      <view class="root-types-grid">
        <view wx:for="{{rootTypes}}"
              wx:key="id"
              class="hu-type-item {{item.selected ? 'selected' : ''}}"
              bindtap="toggleRootType"
              data-id="{{item.id}}">
          <text class="hu-type-name">{{item.name}}</text>
          <text class="hu-type-score">{{item.multiplier}}×底分</text>
        </view>
      </view>
    </view>

    <!-- 加分项 -->
    <view class="section">
      <view class="section-title">
        <text class="title-text">加分项</text>
        <text class="reset-btn" bindtap="resetBonuses">重置</text>
      </view>

      <!-- 自摸选项 -->
      <view class="bonus-group">
        <text class="bonus-group-title">自摸</text>
        <view class="bonus-container">
          <view class="bonus-item {{zimoAddBase ? 'selected' : ''}}"
                bindtap="toggleZimoAddBase">
            <text class="bonus-name">自摸加底</text>
            <text class="bonus-score">+底分</text>
          </view>
          <view class="bonus-item {{zimoAddFan ? 'selected' : ''}}"
                bindtap="toggleZimoAddFan">
            <text class="bonus-name">自摸加番</text>
            <text class="bonus-score">×2</text>
          </view>
        </view>
      </view>

      <!-- 杠牌计数 -->
      <view class="bonus-group">
        <text class="bonus-group-title">杠牌</text>
        <view class="counter-container">
          <view class="counter-item">
            <text class="counter-label">杠牌次数</text>
            <view class="counter-controls">
              <view class="counter-btn" bindtap="changeGangCount" data-type="gang" data-action="minus">-</view>
              <text class="counter-value">{{gangCount}}</text>
              <view class="counter-btn" bindtap="changeGangCount" data-type="gang" data-action="plus">+</view>
            </view>
            <text class="counter-score">{{gangCount > 0 ? '+' + (gangCount * 2) + '分' : ''}}</text>
          </view>
          <view class="counter-item">
            <text class="counter-label">巴杠次数</text>
            <view class="counter-controls">
              <view class="counter-btn" bindtap="changeGangCount" data-type="bagang" data-action="minus">-</view>
              <text class="counter-value">{{bagangCount}}</text>
              <view class="counter-btn" bindtap="changeGangCount" data-type="bagang" data-action="plus">+</view>
            </view>
            <text class="counter-score">{{bagangCount > 0 ? '+' + bagangCount + '分' : ''}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 计算结果 -->
    <view class="result-section">
      <view class="result-container">
        <view class="base-score">
          <text class="label">基础分：</text>
          <text class="score">{{baseScore}}分</text>
        </view>
        <view class="bonus-total">
          <text class="label">加分：</text>
          <text class="score">{{bonusTotal}}分</text>
        </view>
        <view class="final-score">
          <text class="label">总分：</text>
          <text class="score {{isCapped ? 'capped' : ''}}">{{finalScore}}分</text>
          <text wx:if="{{isCapped}}" class="cap-indicator">已封顶</text>
        </view>
      </view>

      <!-- 计算按钮 -->
      <view class="calculate-btn" bindtap="calculateScore">
        <text>重新计算</text>
      </view>
    </view>
  </view>
</scroll-view>
