<!--川麻小助手主页面-->
<scroll-view class="scrollarea" scroll-y type="list">
  <view class="container">
    <!-- 标题区域 -->
    <view class="header">
      <text class="title">川麻小助手</text>
      <text class="subtitle">专业算分工具</text>
    </view>

    <!-- 封顶分数设置 -->
    <view class="section">
      <view class="section-title">
        <text class="title-text">封顶分数</text>
      </view>
      <view class="cap-score-container">
        <slider class="cap-slider"
                min="10"
                max="100"
                step="5"
                value="{{capScore}}"
                bindchange="onCapScoreChange"
                activeColor="#D2691E"
                backgroundColor="#F5DEB3"/>
        <text class="cap-score-text">{{capScore}}分</text>
      </view>
    </view>

    <!-- 胡牌类型选择 -->
    <view class="section">
      <view class="section-title">
        <text class="title-text">胡牌类型</text>
        <text class="reset-btn" bindtap="resetHuTypes">重置</text>
      </view>
      <view class="hu-types-grid">
        <view wx:for="{{huTypes}}"
              wx:key="id"
              class="hu-type-item {{item.selected ? 'selected' : ''}}"
              bindtap="toggleHuType"
              data-id="{{item.id}}">
          <text class="hu-type-name">{{item.name}}</text>
          <text class="hu-type-score">{{item.score}}分</text>
        </view>
      </view>
    </view>

    <!-- 加分项 -->
    <view class="section">
      <view class="section-title">
        <text class="title-text">加分项</text>
        <text class="reset-btn" bindtap="resetBonuses">重置</text>
      </view>
      <view class="bonus-container">
        <view wx:for="{{bonuses}}"
              wx:key="id"
              class="bonus-item {{item.selected ? 'selected' : ''}}"
              bindtap="toggleBonus"
              data-id="{{item.id}}">
          <text class="bonus-name">{{item.name}}</text>
          <text class="bonus-score">+{{item.score}}分</text>
        </view>
      </view>
    </view>

    <!-- 计算结果 -->
    <view class="result-section">
      <view class="result-container">
        <view class="base-score">
          <text class="label">基础分：</text>
          <text class="score">{{baseScore}}分</text>
        </view>
        <view class="bonus-total">
          <text class="label">加分：</text>
          <text class="score">{{bonusTotal}}分</text>
        </view>
        <view class="final-score">
          <text class="label">总分：</text>
          <text class="score {{isCapped ? 'capped' : ''}}">{{finalScore}}分</text>
          <text wx:if="{{isCapped}}" class="cap-indicator">已封顶</text>
        </view>
      </view>

      <!-- 计算按钮 -->
      <view class="calculate-btn" bindtap="calculateScore">
        <text>重新计算</text>
      </view>
    </view>
  </view>
</scroll-view>
